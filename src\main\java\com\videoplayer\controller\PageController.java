package com.videoplayer.controller;

import com.videoplayer.entity.Video;
import com.videoplayer.service.VideoService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Optional;

/**
 * 页面控制器
 * 处理前端页面路由和数据传递
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Controller
public class PageController {

    private final VideoService videoService;

    public PageController(VideoService videoService) {
        this.videoService = videoService;
    }
    
    /**
     * 处理视频URL
     */
    private void processVideoUrl(Video video) {
        if (video != null) {
            if (video.getVideoUrl() != null) {
                video.setVideoUrl(videoService.getVideoUrl(video.getVideoUrl()));
            }
            if (video.getThumbnailUrl() != null) {
                video.setThumbnailUrl(videoService.getVideoUrl(video.getThumbnailUrl()));
            }
        }
    }

    /**
     * 首页 - 显示视频列表
     */
    @GetMapping("/")
    public String index(Model model) {
        try {
            List<Video> popularVideos = videoService.getPopularVideos(12);
            
            model.addAttribute("popularVideos", popularVideos);
            model.addAttribute("pageTitle", "视频播放器 - 首页");
            
            return "index";
        } catch (Exception e) {
            model.addAttribute("error", "加载视频列表失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 所有视频页面
     */
    @GetMapping("/videos")
    public String videos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(required = false) String keyword,
            Model model) {
        try {
            List<Video> videos;
            boolean hasNextPage = false;

            if (keyword != null && !keyword.trim().isEmpty()) {
                videos = videoService.searchVideosList(keyword, page, size);
                model.addAttribute("keyword", keyword);
                // 检查是否有下一页：尝试获取下一页的第一个视频
                List<Video> nextPageVideos = videoService.searchVideosList(keyword, page + 1, 1);
                hasNextPage = !nextPageVideos.isEmpty();
            } else {
                videos = videoService.getAllVideos(page, size);
                // 检查是否有下一页：尝试获取下一页的第一个视频
                List<Video> nextPageVideos = videoService.getAllVideos(page + 1, 1);
                hasNextPage = !nextPageVideos.isEmpty();
            }

            // 处理视频URL
            videos.forEach(this::processVideoUrl);

            model.addAttribute("videos", videos);
            model.addAttribute("currentPage", page);
            model.addAttribute("pageSize", size);
            model.addAttribute("hasNextPage", hasNextPage);
            model.addAttribute("pageTitle", "所有视频");

            return "videos";
        } catch (Exception e) {
            model.addAttribute("error", "加载视频列表失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 视频播放页面
     */
    @GetMapping("/play/{id}")
    public String playVideo(@PathVariable Long id, Model model) {
        try {
            Optional<Video> videoOptional = videoService.getVideoById(id);
            
            if (videoOptional.isPresent()) {
                Video video = videoOptional.get();
                
                // 处理视频URL
                processVideoUrl(video);
                
                model.addAttribute("video", video);
                model.addAttribute("pageTitle", video.getTitle() + " - 视频播放");
                
                return "play";
            } else {
                model.addAttribute("error", "视频不存在或已被删除");
                return "error";
            }
        } catch (Exception e) {
            model.addAttribute("error", "加载视频失败: " + e.getMessage());
            return "error";
        }
    }


    /**
     * 搜索结果页面
     */
    @GetMapping("/search")
    public String searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "12") int size,
            Model model) {
        
        try {
            var videoPage = videoService.searchVideos(keyword, page, size);
            
            model.addAttribute("videos", videoPage.getContent());
            model.addAttribute("keyword", keyword);
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", videoPage.getTotalPages());
            model.addAttribute("totalItems", videoPage.getTotalElements());
            model.addAttribute("pageTitle", "搜索结果: " + keyword);
            
            return "search-results";
        } catch (Exception e) {
            model.addAttribute("error", "搜索失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 管理页面
     */
    @GetMapping("/admin")
    public String adminPanel(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String format,
            Model model) {
        try {
            List<Video> videos;

            if (search != null && !search.trim().isEmpty()) {
                // 搜索视频
                videos = videoService.searchVideosByTitle(search);
                model.addAttribute("search", search);
            } else {
                // 获取所有视频（包括禁用的）
                videos = videoService.getAllVideosForAdmin();
            }

            // 根据状态筛选
            if (status != null && !status.isEmpty()) {
                if ("active".equals(status)) {
                    videos = videos.stream().filter(Video::getIsActive).collect(java.util.stream.Collectors.toList());
                } else if ("inactive".equals(status)) {
                    videos = videos.stream().filter(v -> !v.getIsActive()).collect(java.util.stream.Collectors.toList());
                }
                model.addAttribute("status", status);
            }

            // 根据格式筛选
            if (format != null && !format.isEmpty()) {
                videos = videos.stream()
                    .filter(v -> format.equalsIgnoreCase(v.getVideoFormat()))
                    .collect(java.util.stream.Collectors.toList());
                model.addAttribute("format", format);
            }

            // 处理视频URL
            videos.forEach(this::processVideoUrl);

            model.addAttribute("videos", videos);
            model.addAttribute("pageTitle", "视频管理");

            return "admin";
        } catch (Exception e) {
            model.addAttribute("error", "加载管理页面失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 添加视频页面
     */
    @GetMapping("/admin/add")
    public String addVideoPage(Model model) {
        model.addAttribute("video", new Video());
        model.addAttribute("pageTitle", "添加视频");
        return "add-video";
    }

    /**
     * 编辑视频页面
     */
    @GetMapping("/admin/edit/{id}")
    public String editVideoPage(@PathVariable Long id, Model model) {
        try {
            Optional<Video> videoOptional = videoService.getVideoById(id);
            
            if (videoOptional.isPresent()) {
                model.addAttribute("video", videoOptional.get());
                model.addAttribute("pageTitle", "编辑视频");
                return "edit-video";
            } else {
                model.addAttribute("error", "视频不存在");
                return "error";
            }
        } catch (Exception e) {
            model.addAttribute("error", "加载编辑页面失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 关于页面
     */
    @GetMapping("/about")
    public String about(Model model) {
        model.addAttribute("pageTitle", "关于我们");
        return "about";
    }

    /**
     * 缩略图测试页面
     */
    @GetMapping("/test-thumbnail")
    public String testThumbnail(Model model) {
        try {
            List<Video> videos = videoService.getPopularVideos(4);

            // 处理视频URL（包括缩略图URL）
            for (Video video : videos) {
                processVideoUrl(video);
            }

            model.addAttribute("videos", videos);
            model.addAttribute("pageTitle", "缩略图测试页面");

            return "test-thumbnail";
        } catch (Exception e) {
            model.addAttribute("error", "加载测试页面失败: " + e.getMessage());
            return "error";
        }
    }
}

