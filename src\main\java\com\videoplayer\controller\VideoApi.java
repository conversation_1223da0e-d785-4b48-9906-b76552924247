package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import com.videoplayer.exception.ResourceNotFoundException;
import com.videoplayer.service.VideoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 视频控制器
 * 提供视频相关的REST API接口
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@RestController
@RequestMapping("/api/videos")
@CrossOrigin(origins = "*")
public class VideoApi {

    private static final Logger logger = LoggerFactory.getLogger(VideoApi.class);

    @Autowired
    private VideoService videoService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        try {
            long count = videoService.getTotalVideoCount();
            return ResponseEntity.ok(ApiResponse.success("系统正常，视频总数: " + count));
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("HEALTH_CHECK_FAILED", "系统异常: " + e.getMessage()));
        }
    }

    /**
     * 获取所有视频列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<Video>>> getAllVideos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            logger.info("获取视频列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);

            Page<Video> videoPage = videoService.getAllActiveVideos(page, size, sortBy, sortDir);

            return ResponseEntity.ok(ApiResponse.success(
                videoPage.getContent(),
                videoPage.getTotalElements(),
                page,
                size
            ));
        } catch (Exception e) {
            logger.error("获取视频列表失败 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir, e);
            throw e; // 重新抛出异常，让GlobalExceptionHandler处理
        }
    }

    /**
     * 根据ID获取视频详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> getVideoById(@PathVariable Long id) {
        logger.info("获取视频详情 - id: {}", id);
        
        Optional<Video> optionalVideo = videoService.getVideoById(id);
        
        if (optionalVideo.isEmpty()) {
            throw new ResourceNotFoundException("Video", id);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取视频详情成功", optionalVideo.get()));
    }

    /**
     * 搜索视频
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<Video>>> searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("搜索视频 - keyword: {}, page: {}, size: {}", keyword, page, size);
        
        Page<Video> videoPage = videoService.searchVideos(keyword, page, size);
        
        return ResponseEntity.ok(ApiResponse.success(
            videoPage.getContent(), 
            videoPage.getTotalElements(), 
            page, 
            size
        ));
    }

    /**
     * 添加视频
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Video>> addVideo(@RequestBody Video video) {
        logger.info("添加视频 - title: {}", video.getTitle());

        Video savedVideo = videoService.saveVideo(video);

        return ResponseEntity.ok(ApiResponse.success("添加视频成功", savedVideo));
    }

    /**
     * 更新视频
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> updateVideo(@PathVariable Long id, @RequestBody Video video) {
        logger.info("更新视频 - id: {}", id);

        Video updatedVideo = videoService.updateVideo(id, video);
        if (updatedVideo == null) {
            throw new ResourceNotFoundException("Video", id);
        }

        return ResponseEntity.ok(ApiResponse.success("更新视频成功", updatedVideo));
    }

    /**
     * 删除视频
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteVideo(@PathVariable Long id) {
        logger.info("删除视频 - id: {}", id);

        boolean deleted = videoService.deleteVideo(id);
        if (!deleted) {
            throw new ResourceNotFoundException("Video", id);
        }

        return ResponseEntity.ok(ApiResponse.success("删除视频成功"));
    }
}
