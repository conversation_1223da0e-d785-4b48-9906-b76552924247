package com.videoplayer.repository;

import com.videoplayer.entity.Video;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 视频数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface VideoRepository extends JpaRepository<Video, Long> {
    
    /**
     * 查询所有激活的视频
     */
    Page<Video> findAllByIsActiveTrue(Pageable pageable);
    
    /**
     * 根据标题搜索激活的视频
     */
    Page<Video> findByTitleContainingAndIsActiveTrue(String title, Pageable pageable);
    
    /**
     * 根据标题模糊查询视频
     */
    List<Video> findByTitleContainingIgnoreCase(String title);

    /**
     * 根据标题模糊查询视频（分页）
     */
    Page<Video> findByTitleContainingIgnoreCase(String title, Pageable pageable);

    /**
     * 查询所有启用的视频
     */
    List<Video> findByIsActiveTrue();

    /**
     * 查询所有启用的视频（分页）
     */
    Page<Video> findByIsActiveTrue(Pageable pageable);



    /**
     * 查询最新的视频（按创建时间排序）
     */
    @Query("SELECT v FROM Video v WHERE v.isActive = true ORDER BY v.createdTime DESC")
    List<Video> findLatestVideos(Pageable pageable);

    /**
     * 根据ID查询启用的视频
     */
    Optional<Video> findByIdAndIsActiveTrue(Long id);

    /**
     * 统计总视频数量
     */
    long countByIsActiveTrue();

    /**
     * 根据关键词搜索视频（标题或描述）
     */
    @Query("SELECT v FROM Video v WHERE v.isActive = true AND " +
           "(LOWER(v.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(v.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Video> searchVideos(@Param("keyword") String keyword, Pageable pageable);
}

