package com.videoplayer.service;

import com.videoplayer.entity.Video;
import com.videoplayer.repository.VideoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 视频服务类
 * 提供视频相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class VideoService {

    @Autowired
    private VideoRepository videoRepository;
    
    // OSS配置已简化，使用默认值
    private String ossBaseUrl = "https://example.com";
    private String ossDir = "videos";

    /**
     * 获取所有视频（分页）
     */
    public List<Video> getAllVideos(int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Video> videoPage = videoRepository.findAllByIsActiveTrue(pageable);
        return videoPage.getContent();
    }

    /**
     * 搜索视频（分页，返回列表）
     */
    public List<Video> searchVideosList(String keyword, int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        return videoRepository.findByTitleContainingAndIsActiveTrue(keyword, pageable).getContent();
    }
    
    /**
     * 获取完整的视频URL
     */
    public String getVideoUrl(String videoKey) {
        if (videoKey == null || videoKey.trim().isEmpty()) {
            return "";
        }

        // 如果已经是完整的URL（以http或https开头），检查是否需要清理重复URL
        if (videoKey.toLowerCase().startsWith("http")) {
            // 清理重复的URL前缀
            String baseUrl = ossBaseUrl.endsWith("/") ? ossBaseUrl.substring(0, ossBaseUrl.length() - 1) : ossBaseUrl;
            String directory = ossDir.startsWith("/") ? ossDir.substring(1) : ossDir;
            directory = directory.endsWith("/") ? directory.substring(0, directory.length() - 1) : directory;

            String pattern = baseUrl + "/" + directory + "/";
            // 如果URL中包含多个相同的路径，只保留一个
            if (videoKey.contains(pattern)) {
                int firstIndex = videoKey.indexOf(pattern);
                String fileName = videoKey.substring(firstIndex + pattern.length());
                // 去除文件名中可能包含的重复路径
                if (fileName.contains(pattern)) {
                    fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                }
                return pattern + fileName;
            }
            return videoKey;
        }

        // 处理相对路径
        String baseUrl = ossBaseUrl.endsWith("/") ? ossBaseUrl.substring(0, ossBaseUrl.length() - 1) : ossBaseUrl;
        String directory = ossDir.startsWith("/") ? ossDir.substring(1) : ossDir;
        directory = directory.endsWith("/") ? directory.substring(0, directory.length() - 1) : directory;
        String key = videoKey.startsWith("/") ? videoKey.substring(1) : videoKey;

        // 确保文件名不包含路径
        if (key.contains("/")) {
            key = key.substring(key.lastIndexOf("/") + 1);
        }

        return String.format("%s/%s/%s", baseUrl, directory, key);
    }

    /**
     * 获取所有启用的视频
     */
    public List<Video> getAllActiveVideos() {
        return videoRepository.findByIsActiveTrue();
    }

    /**
     * 获取所有视频（包括禁用的，用于管理页面）
     */
    public List<Video> getAllVideosForAdmin() {
        return videoRepository.findAll(Sort.by(Sort.Direction.DESC, "createdTime"));
    }

    /**
     * 分页获取所有启用的视频
     */
    public Page<Video> getAllActiveVideos(int page, int size, String sortBy, String sortDir) {
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        return videoRepository.findByIsActiveTrue(pageable);
    }

    /**
     * 根据ID获取视频
     */
    public Optional<Video> getVideoById(Long id) {
        return videoRepository.findByIdAndIsActiveTrue(id);
    }

    /**
     * 保存视频
     */
    public Video saveVideo(Video video) {
        return videoRepository.save(video);
    }

    /**
     * 更新视频信息
     */
    public Video updateVideo(Long id, Video videoDetails) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            video.setTitle(videoDetails.getTitle());
            video.setDescription(videoDetails.getDescription());
            video.setVideoUrl(videoDetails.getVideoUrl());
            video.setThumbnailUrl(videoDetails.getThumbnailUrl());
            video.setDuration(videoDetails.getDuration());
            video.setFileSize(videoDetails.getFileSize());
            video.setVideoFormat(videoDetails.getVideoFormat());
            video.setResolution(videoDetails.getResolution());
            return videoRepository.save(video);
        }
        return null;
    }



    /**
     * 删除视频（软删除）
     */
    public boolean deleteVideo(Long id) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            video.setIsActive(false);
            videoRepository.save(video);
            return true;
        }
        return false;
    }

    /**
     * 物理删除视频
     */
    public boolean permanentDeleteVideo(Long id) {
        if (videoRepository.existsById(id)) {
            videoRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * 根据标题搜索视频
     */
    public List<Video> searchVideosByTitle(String title) {
        return videoRepository.findByTitleContainingIgnoreCase(title);
    }

    /**
     * 分页搜索视频
     */
    public Page<Video> searchVideos(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        return videoRepository.searchVideos(keyword, pageable);
    }

    /**
     * 获取最新视频（按创建时间排序）
     */
    public List<Video> getPopularVideos(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return videoRepository.findLatestVideos(pageable);
    }



    /**
     * 获取视频总数
     */
    public long getTotalVideoCount() {
        return videoRepository.countByIsActiveTrue();
    }






}

